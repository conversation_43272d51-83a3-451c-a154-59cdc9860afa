{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-a8a55010ba2ad1af86f1.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "rnpicker_autolinked_build", "jsonFile": "directory-rnpicker_autolinked_build-Debug-97195ce7767e97b4cae3.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni", "targetIndexes": [6]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-f55f335bbf16be789909.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "pagerview_autolinked_build", "jsonFile": "directory-pagerview_autolinked_build-Debug-3a3775806ca2630c28a2.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-b2f1e948bce123a83812.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [7]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-4766f16f71567863a5c9.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [10]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-2b2b9cecfd2e79b076c0.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [8]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-30d06f361ed1ef96d07c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [9]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-aa6e54527d54c630da1d.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-Debug-a2943766eb89ca838ae1.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [1]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-02949dd8e2108f928f69.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 10, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-Debug-803a1da15d3806461343.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-602b0fa51019a648230d.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_pagerview::@7032a8921530ec438d60", "jsonFile": "target-react_codegen_pagerview-Debug-d3213b93789b5c067f51.json", "name": "react_codegen_pagerview", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-5e19e13140ce87974dd7.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-8b3fa58406f778b089d4.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rnpicker::@e8bb2e9e833f47d0d516", "jsonFile": "target-react_codegen_rnpicker-Debug-f0eaa37eb98bea874bec.json", "name": "react_codegen_rnpicker", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-8c8b00a5010da2f0151f.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-566a293dd5aa5f8c7c36.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-dd12f69a6eb69593bfe6.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-b73620399086b47856ea.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "source": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}