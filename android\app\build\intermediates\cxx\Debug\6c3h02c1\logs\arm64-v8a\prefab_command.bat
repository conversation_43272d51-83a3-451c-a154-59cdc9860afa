@echo off
"C:\\Program Files\\Java\\jdk-17\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  arm64-v8a ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  26 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging858793238752773385\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb5951a603c6ef86f8f4868bb3898655\\transformed\\react-android-0.76.9-debug\\prefab" ^
  "C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\5b13v663" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d1409c0ddab461f348a8775be723d6e1\\transformed\\hermes-android-0.76.9-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\102477294d6543ddc195256a38a23f57\\transformed\\fbjni-0.6.0\\prefab"
