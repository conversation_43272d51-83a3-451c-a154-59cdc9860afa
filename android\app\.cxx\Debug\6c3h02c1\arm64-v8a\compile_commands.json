[{"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dappmodules_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"C:/Users/<USER>/Desktop/safaribank/android/app/build/generated/autolinking/src/main/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\C_\\Users\\MSA_WIN10_G\\Desktop\\safaribank\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dappmodules_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"C:/Users/<USER>/Desktop/safaribank/android/app/build/generated/autolinking/src/main/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\RNCAndroidDialogPickerMeasurementsManager.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDialogPickerMeasurementsManager.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDialogPickerMeasurementsManager.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\RNCAndroidDialogPickerShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDialogPickerShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDialogPickerShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\RNCAndroidDialogPickerState.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDialogPickerState.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDialogPickerState.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\RNCAndroidDropdownPickerMeasurementsManager.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDropdownPickerMeasurementsManager.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDropdownPickerMeasurementsManager.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\RNCAndroidDropdownPickerShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDropdownPickerShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDropdownPickerShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\RNCAndroidDropdownPickerState.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDropdownPickerState.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\RNCAndroidDropdownPickerState.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\rnpicker.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\rnpicker.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\rnpicker.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\64b15e53e28cb8271420dc093eb2729f\\jni\\react\\renderer\\components\\rnpicker\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\ce0f8ac390a10c7078ceb651b2b5f869\\codegen\\jni\\react\\renderer\\components\\rnpicker\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\4a6f3b899f2472db525b1aeaec1952cf\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\Props.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\Props.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\ce0f8ac390a10c7078ceb651b2b5f869\\codegen\\jni\\react\\renderer\\components\\rnpicker\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\4a6f3b899f2472db525b1aeaec1952cf\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\States.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\States.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\States.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnpicker_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpicker_autolinked_build\\CMakeFiles\\react_codegen_rnpicker.dir\\64b15e53e28cb8271420dc093eb2729f\\jni\\react\\renderer\\components\\rnpicker\\rnpickerJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\rnpickerJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpicker\\rnpickerJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\pagerview-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\pagerview-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\pagerview-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\Props.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\Props.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\States.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\States.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\States.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\pagerviewJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\pagerviewJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\pagerviewJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\Props.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\States.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\rnreanimated-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3c014a285d30952635ff25c24f3b628c\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\a12763d8e7192a1a1af2beaef787a338\\components\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\89f4c1e374e38ed7d52e8efad6c07ed6\\components\\safeareacontext\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\f503560e2ebe3d68cd54b0b66c851b4b\\renderer\\components\\safeareacontext\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\14f5e74951c6612c6104c971da74cdde\\react\\renderer\\components\\safeareacontext\\Props.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\f503560e2ebe3d68cd54b0b66c851b4b\\renderer\\components\\safeareacontext\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\14f5e74951c6612c6104c971da74cdde\\react\\renderer\\components\\safeareacontext\\States.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\2ed62cb3099698c2101e3d1e9852c7a9\\safeareacontext\\safeareacontextJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\4cfec5323944b6171731b6c48c986167\\source\\codegen\\jni\\safeareacontext-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d1928e7288675d66d0958ec2f22feae5\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\0fecca949a576486bd8918ef885820d1\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\c571c1d6036a168ae4fc3473904c61ef\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\8f3913b0681331b0df1ff2cddf0df947\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\c571c1d6036a168ae4fc3473904c61ef\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\c571c1d6036a168ae4fc3473904c61ef\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\c84445bffdc9bdc8d818cf1df90d11cb\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\9e575de25c10bd66fa7a0876279bb99d\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\0981d4b7e38ed9fcd716e9fcafcebb91\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\070b71a12cd3933f88c5b6415f8a0dd2\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\0981d4b7e38ed9fcd716e9fcafcebb91\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\070b71a12cd3933f88c5b6415f8a0dd2\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d4fd265a310a84ae642e041426a9da74\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\36bb9a9a578d4626246b7d56480f5ac3\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\36bb9a9a578d4626246b7d56480f5ac3\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\rnsvg.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\bdeaecfcbf9bb1797bbc30722cb9fe3c\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\6f8bf51a5055904d319082ce2ec523d8\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\33e91c6f6354d3e633da9eb95b293770\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\6f8bf51a5055904d319082ce2ec523d8\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\33e91c6f6354d3e633da9eb95b293770\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\6f8bf51a5055904d319082ce2ec523d8\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\RNVectorIconsSpec-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\RNCWebViewSpec-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp"}]