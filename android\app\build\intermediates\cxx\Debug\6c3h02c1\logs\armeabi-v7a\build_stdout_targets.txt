ninja: Entering directory `C:\Users\<USER>\Desktop\safaribank\android\app\.cxx\Debug\6c3h02c1\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/94] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/94] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[3/94] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[4/94] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[5/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o
[6/94] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[7/94] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[8/94] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[9/94] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[10/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o
[11/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o
[12/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o
[13/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o
[14/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/4a6f3b899f2472db525b1aeaec1952cf/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o
[15/94] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o
[16/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o
[17/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/64b15e53e28cb8271420dc093eb2729f/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o
[18/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/ce0f8ac390a10c7078ceb651b2b5f869/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:16:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNCAndroidDialogPickerEventEmitter::onSelect(OnSelect $event) const {
                                                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:17:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:17:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
                                               ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:18:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:19:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "position", $event.position);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:19:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "position", $event.position);
                                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:20:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:25:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNCAndroidDialogPickerEventEmitter::onFocus(OnFocus $event) const {
                                                         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:27:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:29:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:34:56: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNCAndroidDialogPickerEventEmitter::onBlur(OnBlur $event) const {
                                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:36:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:38:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:43:62: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNCAndroidDropdownPickerEventEmitter::onSelect(OnSelect $event) const {
                                                             ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:44:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:44:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
                                               ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:45:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:46:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "position", $event.position);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:46:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "position", $event.position);
                                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:47:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:52:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNCAndroidDropdownPickerEventEmitter::onFocus(OnFocus $event) const {
                                                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:54:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:56:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:61:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNCAndroidDropdownPickerEventEmitter::onBlur(OnBlur $event) const {
                                                         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:63:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:65:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:70:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNCPickerEventEmitter::onChange(OnChange $event) const {
                                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:71:28: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("change", [$event=std::move($event)](jsi::Runtime &runtime) {
                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:71:45: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("change", [$event=std::move($event)](jsi::Runtime &runtime) {
                                            ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:72:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:73:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "newValue", $event.newValue);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:73:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "newValue", $event.newValue);
                                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:74:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "newIndex", $event.newIndex);
^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:74:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "newIndex", $event.newIndex);
                                          ^
C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:75:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
35 warnings generated.
[19/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o
[20/94] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[21/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/64b15e53e28cb8271420dc093eb2729f/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o
[22/94] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o
[23/94] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o
[24/94] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o
[25/94] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[26/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/4a6f3b899f2472db525b1aeaec1952cf/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o
[27/94] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/ce0f8ac390a10c7078ceb651b2b5f869/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o
[28/94] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o
[29/94] Linking CXX shared library "C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\cxx\Debug\6c3h02c1\obj\armeabi-v7a\libreact_codegen_rnpicker.so"
[30/94] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o
[31/94] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
[32/94] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o
[33/94] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o
[34/94] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o
[35/94] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o
[36/94] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o
[37/94] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o
[38/94] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o
[39/94] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
[40/94] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o
[41/94] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o
[42/94] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/89f4c1e374e38ed7d52e8efad6c07ed6/components/safeareacontext/EventEmitters.cpp.o
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:16:69: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNCSafeAreaProviderEventEmitter::onInsetsChange(OnInsetsChange $event) const {
                                                                    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:17:34: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("insetsChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                 ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:17:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("insetsChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                  ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:18:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:21:38: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  insets.setProperty(runtime, "top", $event.insets.top);
                                     ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:22:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  insets.setProperty(runtime, "right", $event.insets.right);
                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:23:41: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  insets.setProperty(runtime, "bottom", $event.insets.bottom);
                                        ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:24:39: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  insets.setProperty(runtime, "left", $event.insets.left);
                                      ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:25:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  $payload.setProperty(runtime, "insets", insets);
  ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:29:35: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  frame.setProperty(runtime, "x", $event.frame.x);
                                  ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:30:35: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  frame.setProperty(runtime, "y", $event.frame.y);
                                  ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:31:39: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  frame.setProperty(runtime, "width", $event.frame.width);
                                      ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:32:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  frame.setProperty(runtime, "height", $event.frame.height);
                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:33:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  $payload.setProperty(runtime, "frame", frame);
  ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:35:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
15 warnings generated.
[43/94] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o
[44/94] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o
[45/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c571c1d6036a168ae4fc3473904c61ef/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[46/94] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/14f5e74951c6612c6104c971da74cdde/react/renderer/components/safeareacontext/Props.cpp.o
[47/94] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f503560e2ebe3d68cd54b0b66c851b4b/renderer/components/safeareacontext/ShadowNodes.cpp.o
[48/94] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/89f4c1e374e38ed7d52e8efad6c07ed6/components/safeareacontext/ComponentDescriptors.cpp.o
[49/94] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a12763d8e7192a1a1af2beaef787a338/components/safeareacontext/RNCSafeAreaViewState.cpp.o
[50/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c571c1d6036a168ae4fc3473904c61ef/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[51/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c571c1d6036a168ae4fc3473904c61ef/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[52/94] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3c014a285d30952635ff25c24f3b628c/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[53/94] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4cfec5323944b6171731b6c48c986167/source/codegen/jni/safeareacontext-generated.cpp.o
[54/94] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2ed62cb3099698c2101e3d1e9852c7a9/safeareacontext/safeareacontextJSI-generated.cpp.o
[55/94] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f503560e2ebe3d68cd54b0b66c851b4b/renderer/components/safeareacontext/States.cpp.o
[56/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c571c1d6036a168ae4fc3473904c61ef/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[57/94] Linking CXX shared library "C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\cxx\Debug\6c3h02c1\obj\armeabi-v7a\libreact_codegen_safeareacontext.so"
[58/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[59/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0fecca949a576486bd8918ef885820d1/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[60/94] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/36bb9a9a578d4626246b7d56480f5ac3/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o
[61/94] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/6f8bf51a5055904d319082ce2ec523d8/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:30:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSVGImageEventEmitter::onLoad(OnLoad $event) const {
                                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:31:26: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
                         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:31:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
                                          ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:35:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  source.setProperty(runtime, "width", $event.source.width);
                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:36:41: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  source.setProperty(runtime, "height", $event.source.height);
                                        ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:37:38: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  source.setProperty(runtime, "uri", $event.source.uri);
                                     ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:38:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  $payload.setProperty(runtime, "source", source);
  ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:40:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
9 warnings generated.
[62/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0981d4b7e38ed9fcd716e9fcafcebb91/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[63/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0fecca949a576486bd8918ef885820d1/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[64/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8f3913b0681331b0df1ff2cddf0df947/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[65/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0981d4b7e38ed9fcd716e9fcafcebb91/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[66/94] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/33e91c6f6354d3e633da9eb95b293770/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o
[67/94] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/36bb9a9a578d4626246b7d56480f5ac3/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o
[68/94] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o
[69/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d4fd265a310a84ae642e041426a9da74/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[70/94] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/bdeaecfcbf9bb1797bbc30722cb9fe3c/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o
[71/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0981d4b7e38ed9fcd716e9fcafcebb91/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[72/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e575de25c10bd66fa7a0876279bb99d/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:17:52: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onAppear(OnAppear $event) const {
                                                   ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:19:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:21:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:26:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onDisappear(OnDisappear $event) const {
                                                         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:28:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:30:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:35:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onDismissed(OnDismissed $event) const {
                                                         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:36:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:36:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
                                               ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:37:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:38:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:38:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
                                                  ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:39:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:44:84: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onNativeDismissCancelled(OnNativeDismissCancelled $event) const {
                                                                                   ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:45:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
                                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:45:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                            ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:46:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:47:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:47:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
                                                  ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:48:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:53:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onWillAppear(OnWillAppear $event) const {
                                                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:55:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:57:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:62:66: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onWillDisappear(OnWillDisappear $event) const {
                                                                 ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:64:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:66:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:71:76: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onHeaderHeightChange(OnHeaderHeightChange $event) const {
                                                                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:72:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:72:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:73:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:74:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:74:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
                                                  ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:75:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:80:76: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onTransitionProgress(OnTransitionProgress $event) const {
                                                                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:81:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:81:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:82:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:83:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "progress", $event.progress);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:83:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "progress", $event.progress);
                                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:84:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "closing", $event.closing);
^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:84:42: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "closing", $event.closing);
                                         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:85:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "goingForward", $event.goingForward);
^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:85:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "goingForward", $event.goingForward);
                                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:86:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:91:66: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onGestureCancel(OnGestureCancel $event) const {
                                                                 ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:93:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:95:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:100:86: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onHeaderBackButtonClicked(OnHeaderBackButtonClicked $event) const {
                                                                                     ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:102:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:104:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:109:76: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onSheetDetentChanged(OnSheetDetentChanged $event) const {
                                                                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:110:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("sheetDetentChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:110:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("sheetDetentChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:111:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:112:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "index", $event.index);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:112:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "index", $event.index);
                                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:113:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "isStable", $event.isStable);
^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:113:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "isStable", $event.isStable);
                                          ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:114:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:122:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onAppear(OnAppear $event) const {
                                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:124:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:126:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:131:53: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onDisappear(OnDisappear $event) const {
                                                    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:133:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:135:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:140:53: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onDismissed(OnDismissed $event) const {
                                                    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:141:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:141:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
                                               ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:142:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:143:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:143:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
                                                  ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:144:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:149:79: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onNativeDismissCancelled(OnNativeDismissCancelled $event) const {
                                                                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:150:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
                                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:150:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                            ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:151:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:152:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:152:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
                                                  ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:153:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:158:55: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onWillAppear(OnWillAppear $event) const {
                                                      ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:160:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:162:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:167:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onWillDisappear(OnWillDisappear $event) const {
                                                            ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:169:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:171:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:176:71: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onHeaderHeightChange(OnHeaderHeightChange $event) const {
                                                                      ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:177:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:177:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:178:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:179:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:179:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
                                                  ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:180:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:185:71: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onTransitionProgress(OnTransitionProgress $event) const {
                                                                      ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:186:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:186:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:187:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:188:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "progress", $event.progress);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:188:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "progress", $event.progress);
                                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:189:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "closing", $event.closing);
^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:189:42: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "closing", $event.closing);
                                         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:190:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "goingForward", $event.goingForward);
^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:190:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "goingForward", $event.goingForward);
                                              ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:191:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:196:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onGestureCancel(OnGestureCancel $event) const {
                                                            ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:198:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:200:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:205:81: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onHeaderBackButtonClicked(OnHeaderBackButtonClicked $event) const {
                                                                                ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:207:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:209:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:214:71: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onSheetDetentChanged(OnSheetDetentChanged $event) const {
                                                                      ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:215:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("sheetDetentChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:215:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("sheetDetentChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:216:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:217:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "index", $event.index);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:217:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "index", $event.index);
                                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:218:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "isStable", $event.isStable);
^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:218:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "isStable", $event.isStable);
                                          ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:219:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:225:68: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenStackHeaderConfigEventEmitter::onAttached(OnAttached $event) const {
                                                                   ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:227:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:229:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:234:68: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenStackHeaderConfigEventEmitter::onDetached(OnDetached $event) const {
                                                                   ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:236:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:238:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:244:78: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenStackEventEmitter::onFinishTransitioning(OnFinishTransitioning $event) const {
                                                                             ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:246:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:248:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:253:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onSearchFocus(OnSearchFocus $event) const {
                                                           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:255:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:257:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:262:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onSearchBlur(OnSearchBlur $event) const {
                                                         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:264:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:266:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:271:72: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onSearchButtonPress(OnSearchButtonPress $event) const {
                                                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:272:39: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("searchButtonPress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                      ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:272:56: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("searchButtonPress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:273:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:274:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "text", $event.text);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:274:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "text", $event.text);
                                          ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:275:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:280:72: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onCancelButtonPress(OnCancelButtonPress $event) const {
                                                                       ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:282:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:284:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:289:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onChangeText(OnChangeText $event) const {
                                                         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:290:32: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("changeText", [$event=std::move($event)](jsi::Runtime &runtime) {
                               ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:290:49: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("changeText", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:291:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:292:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "text", $event.text);
    ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:292:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "text", $event.text);
                                          ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:293:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:298:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onClose(OnClose $event) const {
                                               ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:300:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:302:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:307:46: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onOpen(OnOpen $event) const {
                                             ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:309:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:311:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
156 warnings generated.
[73/94] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o
[74/94] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/33e91c6f6354d3e633da9eb95b293770/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o
[75/94] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o
[76/94] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o
[77/94] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o
[78/94] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/6f8bf51a5055904d319082ce2ec523d8/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o
[79/94] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o
[80/94] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o
[81/94] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d4fd265a310a84ae642e041426a9da74/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[82/94] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o
[83/94] Linking CXX shared library "C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\cxx\Debug\6c3h02c1\obj\armeabi-v7a\libreact_codegen_rnscreens.so"
[84/94] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o
[85/94] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o
[86/94] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o
[87/94] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o
[88/94] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o
[89/94] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o
[90/94] Building CXX object CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/safaribank/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[91/94] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o
[92/94] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/bdeaecfcbf9bb1797bbc30722cb9fe3c/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o
[93/94] Linking CXX shared library "C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\cxx\Debug\6c3h02c1\obj\armeabi-v7a\libreact_codegen_rnsvg.so"
[94/94] Linking CXX shared library "C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\cxx\Debug\6c3h02c1\obj\armeabi-v7a\libappmodules.so"
