// import inAppMessaging from '@react-native-firebase/in-app-messaging';
import { router } from 'expo-router';
import messageService from './messageService';

// Dynamic import to avoid build issues
let inAppMessaging = null;

const loadInAppMessaging = async () => {
  try {
    const module = await import('@react-native-firebase/in-app-messaging');
    inAppMessaging = module.default;
    return true;
  } catch (error) {
    console.warn('⚠️ In-App Messaging not available:', error.message);
    return false;
  }
};

class InAppMessagingService {
  constructor() {
    this.isInitialized = false;
    this.messageDisplayListener = null;
    this.messageClickListener = null;
    this.messageDismissListener = null;
    this.messageErrorListener = null;
  }

  async initialize() {
    try {
      console.log('🎯 Initializing In-App Messaging Service...');

      // Load the module dynamically
      const isLoaded = await loadInAppMessaging();
      if (!isLoaded) {
        console.warn('⚠️ In-App Messaging module not available, skipping initialization');
        return;
      }

      // Enable in-app messaging
      await inAppMessaging().setMessagesDisplaySuppressed(false);

      // Set up event listeners
      this.setupEventListeners();

      this.isInitialized = true;
      console.log('✅ In-App Messaging service initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize in-app messaging service:', error);
    }
  }

  setupEventListeners() {
    if (!inAppMessaging) {
      console.warn('⚠️ In-App Messaging not available, skipping event listeners');
      return;
    }

    console.log('👂 Setting up in-app messaging listeners...');

    // Listen for message display events
    this.messageDisplayListener = inAppMessaging().onMessageDisplayed((message) => {
      console.log('📱 In-app message displayed:', message);
      this.handleMessageDisplayed(message);
    });

    // Listen for message click events
    this.messageClickListener = inAppMessaging().onMessageClicked((message) => {
      console.log('👆 In-app message clicked:', message);
      this.handleMessageClicked(message);
    });

    // Listen for message dismiss events
    this.messageDismissListener = inAppMessaging().onMessageDismissed((message) => {
      console.log('❌ In-app message dismissed:', message);
      this.handleMessageDismissed(message);
    });

    // Listen for message error events
    this.messageErrorListener = inAppMessaging().onMessageError((error) => {
      console.error('💥 In-app message error:', error);
      this.handleMessageError(error);
    });
  }

  handleMessageDisplayed(message) {
    // Track message display analytics
    console.log('📊 Tracking message display:', message.campaignId);
    
    // Optionally add to message service for history
    if (message.data && message.data.saveToHistory === 'true') {
      this.saveToMessageHistory(message);
    }
  }

  handleMessageClicked(message) {
    console.log('🎯 Processing message click:', message);
    
    // Handle navigation if specified
    if (message.data && message.data.screen) {
      this.navigateToScreen(message.data.screen, message.data);
    }
    
    // Handle custom actions
    if (message.data && message.data.action) {
      this.handleCustomAction(message.data.action, message.data);
    }
    
    // Save to message history if needed
    if (message.data && message.data.saveToHistory === 'true') {
      this.saveToMessageHistory(message, true); // Mark as clicked
    }
  }

  handleMessageDismissed(message) {
    console.log('📝 Message dismissed:', message.campaignId);
    // Track dismissal analytics if needed
  }

  handleMessageError(error) {
    console.error('🚨 In-app messaging error:', error);
    // Handle error reporting or fallback behavior
  }

  async saveToMessageHistory(message, isClicked = false) {
    try {
      const messageData = {
        conversationId: 'in-app-messages',
        title: message.title || 'In-App Message',
        body: message.body || '',
        type: message.data?.messageType || 'info',
        data: {
          ...message.data,
          campaignId: message.campaignId,
          isClicked,
          source: 'in-app-messaging'
        },
        sender: 'system',
      };

      await messageService.addMessage(messageData);
      console.log('💾 In-app message saved to history');
    } catch (error) {
      console.error('❌ Failed to save in-app message to history:', error);
    }
  }

  navigateToScreen(screenName, data = {}) {
    try {
      console.log('🧭 Navigating to screen:', screenName);
      
      switch (screenName) {
        case 'home':
          router.push('/(tabs)/home/<USER>');
          break;
        case 'profile':
          router.push('/(tabs)/profile/profileScreen');
          break;
        case 'services':
          router.push('/services/servicesScreen');
          break;
        case 'notifications':
          router.push('/notification/notificationScreen');
          break;
        case 'transactions':
          router.push('/latestTransaction/latestTransactionScreen');
          break;
        case 'cards':
          router.push('/cardManagement/addNewCard');
          break;
        default:
          console.log('⚠️ Unknown screen:', screenName, '- opening home');
          router.push('/(tabs)/home/<USER>');
          break;
      }
    } catch (error) {
      console.error('❌ Navigation error:', error);
      router.push('/(tabs)/home/<USER>');
    }
  }

  handleCustomAction(action, data = {}) {
    console.log('⚡ Handling custom action:', action);
    
    switch (action) {
      case 'open_url':
        if (data.url) {
          // Handle URL opening
          console.log('🌐 Opening URL:', data.url);
        }
        break;
      case 'show_notification':
        // Trigger a local notification
        console.log('🔔 Showing notification');
        break;
      case 'update_profile':
        // Navigate to profile update
        router.push('/(tabs)/profile/profileScreen');
        break;
      default:
        console.log('⚠️ Unknown action:', action);
        break;
    }
  }

  // Method to suppress messages (useful for certain app states)
  async suppressMessages(suppress = true) {
    if (!inAppMessaging) {
      console.warn('⚠️ In-App Messaging not available');
      return;
    }

    try {
      await inAppMessaging().setMessagesDisplaySuppressed(suppress);
      console.log(`📵 In-app messages ${suppress ? 'suppressed' : 'enabled'}`);
    } catch (error) {
      console.error('❌ Failed to suppress messages:', error);
    }
  }

  // Method to trigger test message (development only)
  async triggerTestMessage() {
    if (!inAppMessaging) {
      console.warn('⚠️ In-App Messaging not available');
      return;
    }

    if (__DEV__) {
      try {
        // This will trigger any test campaigns you have set up
        await inAppMessaging().triggerEvent('test_event');
        console.log('🧪 Test event triggered');
      } catch (error) {
        console.error('❌ Failed to trigger test event:', error);
      }
    }
  }

  // Cleanup method
  cleanup() {
    console.log('🧹 Cleaning up in-app messaging service...');
    
    if (this.messageDisplayListener) {
      this.messageDisplayListener();
      this.messageDisplayListener = null;
    }
    
    if (this.messageClickListener) {
      this.messageClickListener();
      this.messageClickListener = null;
    }
    
    if (this.messageDismissListener) {
      this.messageDismissListener();
      this.messageDismissListener = null;
    }
    
    if (this.messageErrorListener) {
      this.messageErrorListener();
      this.messageErrorListener = null;
    }
    
    this.isInitialized = false;
  }

  // Get initialization status
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      hasListeners: !!(this.messageDisplayListener && this.messageClickListener)
    };
  }
}

// Create and export singleton instance
const inAppMessagingService = new InAppMessagingService();
export default inAppMessagingService;