{"installationFolder": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 26, "abiStl": "c++_shared", "abiLibrary": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\29t1n715\\obj\\arm64-v8a\\libreanimated.so", "abiAndroidGradleBuildJsonFile": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\29t1n715\\arm64-v8a\\android_gradle_build.json"}]}, {"moduleName": "worklets", "moduleHeaders": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\worklets", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 26, "abiStl": "c++_shared", "abiLibrary": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\29t1n715\\obj\\arm64-v8a\\libworklets.so", "abiAndroidGradleBuildJsonFile": "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\29t1n715\\arm64-v8a\\android_gradle_build.json"}]}]}}